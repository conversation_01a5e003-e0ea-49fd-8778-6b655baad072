<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>nchd12</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6220000::V6.22::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>MSPM0G3507</Device>
          <Vendor>Texas Instruments</Vendor>
          <PackID>TexasInstruments.MSPM0G1X0X_G3X0X_DFP.1.3.1</PackID>
          <PackURL>https://software-dl.ti.com/msp430/esd/MSPM0-CMSIS/MSPM0G1X0X_G3X0X/latest/exports/</PackURL>
          <Cpu>IRAM(0x20000000,0x00008000) IRAM2(0x20100000,0x00008000) IROM(0x00000000,0x00020000) IROM2(0x00400000,0x00020000) XRAM(0x20200000,0x00008000) XRAM2(0x20300000,0x00008000) CPUTYPE("Cortex-M0+") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC8000 -FN1 -FF0MSPM0G1X0X_G3X0X_MAIN_128KB -FS00 -FL020000 -FP0($$Device:MSPM0G3507$02_Flash_Programming\FlashARM\MSPM0G1X0X_G3X0X_MAIN_128KB.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:MSPM0G3507$03_SVD\MSPM0G350X.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>ncontroller</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C "$P../../../tools/keil/syscfg.bat '$P' ncontroller.syscfg"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>cmd.exe /C "$P../../../tools/keil/gentxt.bat '$P' ncontroller"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -MPU </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0+</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0+</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0+"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>1</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>4</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </IROM>
              <XRAM>
                <Type>1</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0x8000</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x400000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x20300000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20100000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>4</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>__MSPM0G3507__</Define>
              <Undefine></Undefine>
              <IncludePath>..\source\third_party\CMSIS\Core\Include;..\source;..\;..\ndrivers</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\mspm0g3507.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Source</GroupName>
          <Files>
            <File>
              <FileName>dl_adc12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_adc12.c</FilePath>
            </File>
            <File>
              <FileName>dl_aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_aes.c</FilePath>
            </File>
            <File>
              <FileName>dl_aesadv.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_aesadv.c</FilePath>
            </File>
            <File>
              <FileName>dl_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_common.c</FilePath>
            </File>
            <File>
              <FileName>dl_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_crc.c</FilePath>
            </File>
            <File>
              <FileName>dl_crcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_crcp.c</FilePath>
            </File>
            <File>
              <FileName>dl_dac12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_dac12.c</FilePath>
            </File>
            <File>
              <FileName>dl_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_dma.c</FilePath>
            </File>
            <File>
              <FileName>dl_flashctl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_flashctl.c</FilePath>
            </File>
            <File>
              <FileName>dl_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_i2c.c</FilePath>
            </File>
            <File>
              <FileName>dl_keystorectl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_keystorectl.c</FilePath>
            </File>
            <File>
              <FileName>dl_lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_lcd.c</FilePath>
            </File>
            <File>
              <FileName>dl_lfss.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_lfss.c</FilePath>
            </File>
            <File>
              <FileName>dl_mathacl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_mathacl.c</FilePath>
            </File>
            <File>
              <FileName>dl_mcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_mcan.c</FilePath>
            </File>
            <File>
              <FileName>dl_opa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_opa.c</FilePath>
            </File>
            <File>
              <FileName>dl_rtc_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_rtc_common.c</FilePath>
            </File>
            <File>
              <FileName>dl_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_spi.c</FilePath>
            </File>
            <File>
              <FileName>dl_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_timer.c</FilePath>
            </File>
            <File>
              <FileName>dl_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_trng.c</FilePath>
            </File>
            <File>
              <FileName>dl_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_uart.c</FilePath>
            </File>
            <File>
              <FileName>dl_vref.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\dl_vref.c</FilePath>
            </File>
            <File>
              <FileName>dl_interrupt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\m0p\dl_interrupt.c</FilePath>
            </File>
            <File>
              <FileName>dl_sysctl_mspm0g1x0x_g3x0x.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ndriver</GroupName>
          <Files>
            <File>
              <FileName>drv_oled.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ndrivers\drv_oled.c</FilePath>
            </File>
            <File>
              <FileName>glcdfont.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ndrivers\glcdfont.c</FilePath>
            </File>
            <File>
              <FileName>ssd1306.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ndrivers\ssd1306.c</FilePath>
            </File>
            <File>
              <FileName>soft_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ndrivers\soft_i2c.c</FilePath>
            </File>
            <File>
              <FileName>nchd12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ndrivers\nchd12.c</FilePath>
            </File>
            <File>
              <FileName>gray_detection.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ndrivers\gray_detection.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\main.c</FilePath>
            </File>
            <File>
              <FileName>ncontroller.syscfg</FileName>
              <FileType>5</FileType>
              <FilePath>..\ncontroller.syscfg</FilePath>
            </File>
            <File>
              <FileName>ti_msp_dl_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\ti_msp_dl_config.c</FilePath>
            </File>
            <File>
              <FileName>ti_msp_dl_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\ti_msp_dl_config.h</FilePath>
            </File>
            <File>
              <FileName>startup_mspm0g350x_uvision.s</FileName>
              <FileType>2</FileType>
              <FilePath>.\startup_mspm0g350x_uvision.s</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>soft_i2c_oled</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
