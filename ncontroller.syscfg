/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO          = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1         = GPIO.addInstance();
const GPIO2         = GPIO.addInstance();
const GPIO3         = GPIO.addInstance();
const GPIO4         = GPIO.addInstance();
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                         = "USER_GPIO";
GPIO1.port                          = "PORTB";
GPIO1.associatedPins[0].$name       = "LED";
GPIO1.associatedPins[0].pin.$assign = "PB26";

const Board                       = scripting.addModule("/ti/driverlib/Board", {}, false);
Board.peripheral.$assign          = "DEBUGSS";
Board.peripheral.swclkPin.$assign = "PA20";
Board.peripheral.swdioPin.$assign = "PA19";

GPIO2.$name                          = "PORTA";
GPIO2.port                           = "PORTA";
GPIO2.associatedPins[0].$name        = "OLED_SCL";
GPIO2.associatedPins[0].initialValue = "SET";
GPIO2.associatedPins[0].pin.$assign  = "PA17";

GPIO3.$name                          = "NCHD12_PORT";
GPIO3.port                           = "PORTA";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name        = "SCL";
GPIO3.associatedPins[0].initialValue = "SET";
GPIO3.associatedPins[0].pin.$assign  = "PA0";
GPIO3.associatedPins[1].$name        = "SDA";
GPIO3.associatedPins[1].initialValue = "SET";
GPIO3.associatedPins[1].pin.$assign  = "PA1";

GPIO4.$name                          = "PORTB";
GPIO4.port                           = "PORTB";
GPIO4.associatedPins[0].$name        = "OLED_SDA";
GPIO4.associatedPins[0].initialValue = "SET";
GPIO4.associatedPins[0].pin.$assign  = "PB15";

SYSCTL.clockTreeEn = true;

ProjectConfig.deviceSpin = "MSPM0G3507";
