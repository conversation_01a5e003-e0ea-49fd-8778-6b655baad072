测试本例程需要的硬件如下：
1、MSPM0G3507PMR核心板
2、NCHD12十二路灰度传感器（带I2C输出版本）
3、0.96寸128*64 I2C驱动的OLED显示屏
4、当灰度传感器上地址设置区焊盘全部悬空时（低电平），默认I2C读取地址为0x41，写地址为0x40
_____________________________________________________________________________________
硬件连接：
MSPM0G3507核心板的PA0------>NCHD12灰度传感器的I2C接口的SCL
MSPM0G3507核心板的PA1------>NCHD12灰度传感器的I2C接口的SDA

MSPM0G3507核心板的PA17------>0.96寸OLED显示屏的I2C接口的SCL
MSPM0G3507核心板的PB15------>0.96寸OLED显示屏的I2C接口的SDA
_____________________________________________________________________________________
实验现象：
1、显示屏能显示每一路灰度传感器的状态，0表示低电平、1表示高电平
2、以10mm、20mm黑色引导线为例，将12路灰度传感器线性阵列状态转换成位置偏差
